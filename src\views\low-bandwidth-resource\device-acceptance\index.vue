<template>
  <el-card class="device-acceptance-container">
    <!-- 搜索表单 -->
    <el-form
      :model="searchForm"
      ref="searchForm"
      :inline="true"
      class="search-form"
    >
      <el-form-item label="供应商">
        <el-select
          v-model="searchForm.supplier_uuid"
          placeholder="请选择供应商"
          filterable
          clearable
        >
          <el-option
            v-for="item in supplierOptions"
            :key="item.uuid"
            :label="item.supplier_name"
            :value="item.uuid"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="管理IP">
        <el-input
          v-model.trim="searchForm.manager_ip"
          placeholder="多条用逗号隔开"
        ></el-input>
      </el-form-item>
      <el-form-item label="设备SN">
        <el-input
          v-model.trim="searchForm.sn"
          placeholder="多条用逗号隔开"
        ></el-input>
      </el-form-item>
      <el-form-item label="验收状态">
        <el-select
          v-model="searchForm.acceptance_status"
          placeholder="请选择验收状态"
          clearable
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button
          type="primary"
          :disabled="selectedRows.length === 0"
          @click="handleBatchCommit"
          :loading="batchCommitLoading"
          >交维</el-button
        >
        <el-button
          type="danger"
          :disabled="selectedRows.length === 0"
          @click="handleBatchDelete"
          :loading="batchDeleteLoading"
          >批量删除</el-button
        >
      </el-form-item>
      <auth-status />
    </el-form>

    <!-- 数据表格 -->
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column
        prop="supplier_name"
        label="供应商名称"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="supplier_uuid"
        label="供应商UUID"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="manager_ip"
        label="管理IP"
        width="120"
      ></el-table-column>
      <el-table-column prop="sn" label="设备SN" width="120"></el-table-column>
      <el-table-column prop="line_count" label="线路数" width="60"></el-table-column>
      <el-table-column prop="isp" label="运营商" width="100"></el-table-column>
      <el-table-column
        prop="province"
        label="省份"
        width="100"
      ></el-table-column>
      <el-table-column prop="city" label="城市" width="100"></el-table-column>
      <el-table-column prop="lake" label="节点" width="100"></el-table-column>
      <el-table-column prop="acceptance_status" label="验收状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.acceptance_status)">
            {{ getStatusText(scope.row.acceptance_status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="acceptance_msg"
        label="验收信息"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="acceptance_info"
        label="验收记录"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column prop="dial_status" label="拨号状态" width="100">
        <template slot-scope="scope">
          {{ {
            0: '拨号中',
            1: '拨号成功',
            2: '拨号失败',
          }[scope.row.dial_status] || scope.row.dial_status }}
        </template>
      </el-table-column>
      <el-table-column prop="dial_err_msg" label="拨号失败原因" width="150">
        <template slot-scope="scope">
          {{ scope.row.dial_err_msg || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="scan_status" label="扫描状态" width="100">
        <template slot-scope="scope">
          {{ {
            0: '扫描中',
            1: '扫描成功',
            2: '扫描失败',
          }[scope.row.scan_status] || scope.row.scan_status }}
        </template>
      </el-table-column>
      <el-table-column prop="scan_err_msg" label="扫描失败原因" width="150"></el-table-column>
      <el-table-column prop="pressure_test_status" label="压测状态" width="100">
        <template slot-scope="scope">
          {{ {
            0: '待压测',
            1: '压测中',
            2: '压测成功',
            3: '压测失败',
          }[scope.row.pressure_test_status] || scope.row.pressure_test_status }}
        </template>
      </el-table-column>
      <el-table-column prop="pressure_test_err_msg" label="压测失败原因" width="150"></el-table-column>
      <el-table-column
        prop="create_time"
        label="创建时间"
        width="160"
        :formatter="dateTimeFormatter"
      ></el-table-column>
      <el-table-column
        prop="update_time"
        label="更新时间"
        width="160"
        :formatter="dateTimeFormatter"
      ></el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="handleUpdateInfo(scope.row)"
            >更新验收记录</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </div>

    <!-- 更新验收信息弹窗 -->
    <el-dialog
      append-to-body
      title="更新验收信息"
      :visible.sync="updateDialogVisible"
      width="500px"
    >
      <el-form :model="updateForm" ref="updateForm" label-width="100px">
        <el-form-item label="验收信息" prop="acceptance_info">
          <el-input
            type="textarea"
            v-model="updateForm.acceptance_info"
            :rows="4"
            placeholder="请输入验收信息"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="updateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitUpdateInfo">确定</el-button>
      </div>
    </el-dialog>

    <!-- 认证弹窗 -->
    <auth-verify-dialog
      :visible.sync="authDialogVisible"
      @verify-success="handleAuthVerifySuccess"
      @close="handleAuthDialogClose"
    />
  </el-card>
</template>

<script>
import http from "../http";
import authMixin from "@/mixins/authMixin";
import dayjs from "dayjs";

export default {
  name: "DeviceAcceptance",
  mixins: [authMixin],
  data() {
    return {
      // 搜索表单
      searchForm: {
        supplier_uuid: "",
        manager_ip: "",
        sn: "",
        acceptance_status: "",
        page: 1,
        page_size: 10,
      },
      // 表格数据
      tableData: [],
      tableLoading: false,
      // 分页信息
      pagination: {
        total: 0,
        page: 1,
        page_size: 10,
      },
      // 选中的行
      selectedRows: [],
      // 供应商选项
      supplierOptions: [],
      // 验收状态选项
      statusOptions: [
        { value: 0, label: "待验收" },
        { value: 1, label: "验收中" },
        { value: 2, label: "验收成功" },
        { value: 3, label: "验收失败" },
      ],
      // 更新验收信息弹窗
      updateDialogVisible: false,
      updateForm: {
        id: null,
        acceptance_info: "",
      },
      // 当前操作的行数据
      currentRow: null,
      batchCommitLoading: false,
      batchDeleteLoading: false,
    };
  },
  created() {
    this.fetchSuppliers();
  },
  methods: {
    dateTimeFormatter(row, _, v) {
      return dayjs(v * 1000).format("YYYY-MM-DD HH:mm:ss");
    },
    // 获取供应商列表
    async fetchSuppliers() {
      try {
        const res = await http.getAllSuppliers();
        this.supplierOptions = res.data || [];
      } catch (error) {
        console.error("获取供应商列表出错:", error);
        this.$message.error("获取供应商列表出错");
      }
    },
    // 查询设备验收信息列表
    async fetchDeviceAcceptanceList() {
      this.tableLoading = true;
      try {
        // 处理管理IP和设备SN，将逗号分隔的字符串转为数组
        const params = {
          ...this.searchForm,
          manager_ip: this.searchForm.manager_ip.split(",").filter(Boolean),
          sn: this.searchForm.sn.split(",").filter(Boolean),
        };
        // 如果验收状态为空，则不传该字段
        if (params.acceptance_status === "") {
          delete params.acceptance_status;
        }

        const res = await http.queryDeviceAcceptanceList(params);
        this.tableData = res.data.items || [];
        this.pagination = {
          total: res.data.total || 0,
          page: res.data.page || 1,
          page_size: res.data.page_size || 10,
        };
      } catch (error) {
        console.error("查询设备验收信息出错:", error);
        this.$message.error("查询设备验收信息出错");
      } finally {
        this.tableLoading = false;
      }
    },
    // 处理查询
    handleSearch() {
      this.pagination.page = 1;
      this.fetchDeviceAcceptanceList();
    },
    // 重置表单
    resetForm() {
      this.$refs.searchForm.resetFields();
      this.pagination.page = 1;
    },
    // 处理页码变化
    handleCurrentChange(page) {
      this.pagination.page = page;
      this.searchForm.page = page;
      this.fetchDeviceAcceptanceList();
    },
    // 处理每页条数变化
    handleSizeChange(size) {
      this.pagination.page_size = size;
      this.searchForm.page_size = size;
      this.fetchDeviceAcceptanceList();
    },
    // 处理选择行变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    // 获取验收状态文本
    getStatusText(status) {
      const statusMap = {
        0: "待验收",
        1: "验收中",
        2: "验收成功",
        3: "验收失败",
      };
      return statusMap[status] || "未知状态";
    },
    // 获取验收状态标签类型
    getStatusType(status) {
      const typeMap = {
        0: "info",
        1: "warning",
        2: "success",
        3: "danger",
      };
      return typeMap[status] || "info";
    },
    // 处理更新验收信息
    handleUpdateInfo(row) {
      this.currentRow = row;
      this.updateForm = {
        id: row.id,
        acceptance_info: row.acceptance_info || "",
      };
      this.updateDialogVisible = true;
    },
    // 提交更新验收信息
    submitUpdateInfo() {
      this.executeAuthenticatedOperation("update_info", this.doUpdateInfo);
    },
    // 执行更新验收信息
    async doUpdateInfo() {
      try {
        const res = await http.updateDeviceAcceptanceInfo(this.updateForm);
        this.$message.success("更新验收信息成功");
        this.updateDialogVisible = false;
        this.fetchDeviceAcceptanceList(); // 重新查询列表
      } catch (error) {
        console.error("更新验收信息出错:", error);
        this.$message.error("更新验收信息出错");
      }
    },
    // 处理批量交维
    handleBatchCommit() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请选择要交维的设备");
        return;
      }
      this.executeAuthenticatedOperation("batch_commit", () => {
        this.authDialogVisible = false;
        this.doBatchCommit();
      });
    },
    // 执行批量交维
    async doBatchCommit() {
      try {
        this.batchCommitLoading = true;
        const ids = this.selectedRows.map((row) => row.id);
        const res = await http.commitDevices({
          ids,
          operator: window.localStorage.getItem("userInfo"),
        });

        if (res) {
          this.$message.success("交维操作成功");
          this.fetchDeviceAcceptanceList(); // 重新查询列表
        }
      } catch (error) {
        console.error("交维操作出错:", error);
        this.$message.error("交维操作出错");
      } finally {
        this.batchCommitLoading = false;
      }
    },
    // 处理批量删除
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请选择要删除的设备");
        return;
      }
      
      this.$confirm(
        `确定要删除选中的 ${this.selectedRows.length} 条记录吗？`,
        "批量删除确认",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.executeAuthenticatedOperation("batch_delete", () => {
            this.authDialogVisible = false;
            this.doBatchDelete();
          });
        })
        .catch(() => {
          // 用户取消删除
        });
    },
    // 执行批量删除
    async doBatchDelete() {
      try {
        this.batchDeleteLoading = true;
        const ids = this.selectedRows.map((row) => row.id);
        const res = await http.deleteDeviceAcceptance({
          data: ids,
        });

        if (res) {
          this.$message.success("批量删除成功");
          this.fetchDeviceAcceptanceList(); // 重新查询列表
        }
      } catch (error) {
        console.error("批量删除出错:", error);
        this.$message.error("批量删除出错");
      } finally {
        this.batchDeleteLoading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.device-acceptance-container {
  .search-form {
    background-color: #fff;
    border-radius: 4px;
  }

  .table-operations {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
