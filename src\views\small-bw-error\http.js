import { ajax } from "@/api/ajax";
import { formPromise } from "@/utils/fetch";

const prefix = "api/v1"
export default {
  // 获取故障列表
  async getFaultsList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/pcdn/faults/query`, { params }))
  },
  // 更新故障状态（机器粒度）
  async updateFaultStatus(params) {
    return await formPromise(ajax.put(`${prefix}/sda/pcdn/faults/update`, params))
  },
  // 更新故障状态（线路粒度）
  async updateLineFaultStatus(params) {
    return await formPromise(ajax.put(`${prefix}/sda/pcdn/faults/update_line`, params))
  },
  // 获取故障详情
  async getFaultDetail(id) {
    return await formPromise(ajax.get(`${prefix}/sda/pcdn/faults/detail/${id}`))
  },
  // 获取省份参数
  async getProvinceParams() {
    return await formPromise(ajax.get(`${prefix}/sda/low_bw_resource/param/province`))
  },
  // 获取供应商参数
  async getSupplierParams() {
    return await formPromise(ajax.get(`${prefix}/sda/supplier/simple_list`))
  },
  // 获取运营商参数
  async getIspParams() {
    return await formPromise(ajax.get(`${prefix}/sda/low_bw_resource/param/isp`))
  }
}
